<template>
  <div class="matt-search-form" :class="[variantClass, props.class]">
    <div :class="containerClass">
      <!-- 搜索表单 -->
      <form @submit.prevent="handleSearch" class="space-y-4">
        <!-- 主要搜索区域 -->
        <div class="flex flex-wrap items-center gap-3 lg:gap-4">
          <!-- 自定义字段 -->
          <template v-if="fields && fields.length > 0">
            <div
              v-for="field in visibleFields"
              :key="field.name"
              :class="['flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2', field.class, getFieldWidth(field)]"
            >
              <!-- 字段标签（可选） -->
              <Label
                v-if="shouldShowLabel(field)"
                :for="field.name"
                class="text-sm font-medium text-muted-foreground whitespace-nowrap"
              >
                {{ field.label }}
                <span v-if="field.required" class="text-destructive">*</span>
              </Label>

              <!-- 输入框 -->
              <div v-if="field.type === 'input'" class="relative min-w-0 flex-1">
                <component
                  v-if="field.icon"
                  :is="getIcon(field.icon)"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                />
                <Input
                  :id="field.name"
                  v-model="formValues[field.name]"
                  :placeholder="field.placeholder"
                  :required="field.required"
                  :class="['min-w-0 flex-1', field.icon ? 'pl-10' : '']"
                  size="sm"
                />
              </div>

              <!-- 数字输入框 -->
              <div v-else-if="field.type === 'number'" class="relative min-w-0 flex-1">
                <component
                  v-if="field.icon"
                  :is="getIcon(field.icon)"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                />
                <Input
                  :id="field.name"
                  v-model="formValues[field.name]"
                  type="number"
                  :placeholder="field.placeholder"
                  :required="field.required"
                  :class="['min-w-0 flex-1', field.icon ? 'pl-10' : '']"
                  size="sm"
                />
              </div>

              <!-- 选择框 -->
              <Select v-else-if="field.type === 'select'" v-model="formValues[field.name]">
                <SelectTrigger :id="field.name" class="min-w-0 flex-1 h-9">
                  <SelectValue :placeholder="field.placeholder || '请选择'" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in field.options"
                    :key="option.value"
                    :value="option.value"
                  >
                    <div class="flex items-center gap-2">
                      <component v-if="option.icon" :is="getIcon(option.icon)" class="h-4 w-4" />
                      {{ option.label }}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              <!-- 日期选择 -->
              <div v-else-if="field.type === 'date'" class="relative min-w-0 flex-1">
                <component
                  v-if="field.icon"
                  :is="getIcon(field.icon)"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                />
                <Input
                  :id="field.name"
                  v-model="formValues[field.name]"
                  type="date"
                  :required="field.required"
                  :class="['min-w-0 flex-1', field.icon ? 'pl-10' : '']"
                  size="sm"
                />
              </div>

              <!-- 日期范围选择 -->
              <div
                v-else-if="field.type === 'daterange'"
                class="flex items-center gap-2 min-w-0 flex-1"
              >
                <div class="relative flex-1 min-w-0">
                  <component
                    v-if="field.icon"
                    :is="getIcon(field.icon)"
                    class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                  />
                  <Input
                    v-model="formValues[`${field.name}Start`]"
                    type="date"
                    placeholder="开始日期"
                    :class="['flex-1 min-w-0', field.icon ? 'pl-10' : '']"
                    size="sm"
                  />
                </div>
                <span class="text-muted-foreground">-</span>
                <div class="relative flex-1 min-w-0">
                  <component
                    v-if="field.icon"
                    :is="getIcon(field.icon)"
                    class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                  />
                  <Input
                    v-model="formValues[`${field.name}End`]"
                    type="date"
                    placeholder="结束日期"
                    :class="['flex-1 min-w-0', field.icon ? 'pl-10' : '']"
                    size="sm"
                  />
                </div>
              </div>
            </div>
          </template>

          <!-- 快捷筛选按钮 -->
          <div class="flex flex-wrap items-center gap-2 sm:gap-3">
            <!-- 时间排序 -->
            <Select
              v-if="showTimeSort"
              v-model="formValues.timeSort"
              @update:model-value="handleTimeSortChange"
            >
              <SelectTrigger class="w-auto h-9 gap-2">
                <Calendar class="h-4 w-4" />
                <SelectValue placeholder="时间排序" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">不排序</SelectItem>
                <SelectItem value="desc">最新优先</SelectItem>
                <SelectItem value="asc">最早优先</SelectItem>
              </SelectContent>
            </Select>

            <!-- 状态筛选 -->
            <Select
              v-if="showStatusFilter && statusOptions"
              v-model="formValues.status"
              @update:model-value="handleStatusChange"
            >
              <SelectTrigger class="w-auto h-9 gap-2">
                <Filter class="h-4 w-4" />
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem
                  v-for="option in statusOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  <div class="flex items-center gap-2">
                    <component v-if="option.icon" :is="getIcon(option.icon)" class="h-4 w-4" />
                    {{ option.label }}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- 筛选列表 -->
            <Select
              v-if="showFilterList && filterListOptions"
              v-model="formValues.filter"
              @update:model-value="handleFilterChange"
            >
              <SelectTrigger class="w-auto h-9 gap-2">
                <Tag class="h-4 w-4" />
                <SelectValue placeholder="快捷筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem
                  v-for="option in filterListOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  <div class="flex items-center gap-2">
                    <component v-if="option.icon" :is="getIcon(option.icon)" class="h-4 w-4" />
                    {{ option.label }}
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- 列筛选 -->
            <DropdownMenu v-if="showColumnFilter && columnFilterOptions">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" size="sm" class="h-9 gap-2">
                  <Settings class="h-4 w-4" />
                  列设置
                  <Badge variant="secondary" class="ml-1 h-5 px-1.5 text-xs">
                    {{ visibleColumns.length }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-56">
                <DropdownMenuLabel class="flex items-center justify-between">
                  <span>选择显示的列</span>
                  <span class="text-xs text-muted-foreground">
                    {{ visibleColumns.length }}/{{ columnFilterOptions?.length || 0 }}
                  </span>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="max-h-64 overflow-y-auto">
                  <DropdownMenuCheckboxItem
                    v-for="option in columnFilterOptions"
                    :key="option.key"
                    :checked="visibleColumns.includes(option.key)"
                    :disabled="!option.hideable"
                    @update:checked="checked => handleColumnToggle(option.key, checked)"
                  >
                    {{ option.label }}
                    <span v-if="!option.hideable" class="text-xs text-muted-foreground ml-auto">
                      必需
                    </span>
                  </DropdownMenuCheckboxItem>
                </div>
                <DropdownMenuSeparator />
                <div class="p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    class="w-full justify-start text-xs"
                    @click="resetColumnVisibility"
                  >
                    重置为默认
                  </Button>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <!-- 操作按钮区域 -->
          <div class="flex flex-wrap items-center gap-2 sm:gap-3 w-full sm:w-auto sm:ml-auto justify-center sm:justify-end">
            <!-- 展开/收起按钮 -->
            <Button
              v-if="collapsible && fields && fields.length > fieldsPerRow"
              type="button"
              variant="ghost"
              size="sm"
              class="h-9"
              @click="toggleExpanded"
            >
              <ChevronDown
                :class="['h-4 w-4 transition-transform', isExpanded ? 'rotate-180' : '']"
              />
              {{ isExpanded ? '收起' : '展开' }}
            </Button>

            <!-- 重置按钮 -->
            <Button
              v-if="showReset"
              type="button"
              variant="outline"
              size="sm"
              class="h-9 gap-2"
              @click="handleReset"
              :disabled="loading"
            >
              <RotateCcw class="h-4 w-4" />
              重置
            </Button>

            <!-- 搜索按钮 -->
            <Button v-if="showSearch" type="submit" size="sm" class="h-9 gap-2" :disabled="loading">
              <Search class="h-4 w-4" />
              {{ loading ? '搜索中...' : '搜索' }}
            </Button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import {
  ChevronDown,
  Search,
  RotateCcw,
  Settings,
  Calendar,
  Filter,
  Tag,
  Play,
  Square,
  AlertCircle,
  CalendarDays,
  Activity,
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import type { MattSearchFormProps, MattSearchFormEmits, MattSearchFormValues } from './types'

const props = withDefaults(defineProps<MattSearchFormProps>(), {
  showSearch: true,
  showReset: true,
  showTimeSort: false,
  showStatusFilter: false,
  showFilterList: false,
  showColumnFilter: false,
  collapsible: false,
  defaultExpanded: true,
  fieldsPerRow: 3,
  loading: false,
  showLabels: false,
  variant: 'default',
  bordered: true,
})

const emit = defineEmits<MattSearchFormEmits>()

// 响应式数据
const formValues = ref<MattSearchFormValues>({})
const isExpanded = ref(props.defaultExpanded)
const visibleColumns = ref<string[]>([])

// 初始化表单值
const initFormValues = () => {
  const values: MattSearchFormValues = {}

  if (props.fields) {
    props.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        values[field.name] = field.defaultValue
      }

      // 日期范围字段初始化
      if (field.type === 'daterange') {
        values[`${field.name}Start`] = ''
        values[`${field.name}End`] = ''
      }
    })
  }

  formValues.value = values
}

// 初始化可见列
const initVisibleColumns = () => {
  if (props.columnFilterOptions) {
    visibleColumns.value = props.columnFilterOptions
      .filter(option => option.defaultVisible !== false)
      .map(option => option.key)
  }
}

// 监听字段变化，重新初始化表单
watch(() => props.fields, initFormValues, { immediate: true })

// 监听列配置变化，重新初始化可见列
watch(() => props.columnFilterOptions, initVisibleColumns, { immediate: true })

// 计算属性
const variantClass = computed(() => {
  const variants = {
    default: 'matt-search-form--default',
    compact: 'matt-search-form--compact',
    minimal: 'matt-search-form--minimal',
  }
  return variants[props.variant]
})

const containerClass = computed(() => {
  return [
    props.bordered ? 'bg-background/50 backdrop-blur-sm border rounded-lg p-4' : 'p-2',
    props.variant === 'minimal' ? 'border-none bg-transparent p-0' : '',
  ]
})

const visibleFields = computed(() => {
  if (!props.fields) return []

  if (props.collapsible && !isExpanded.value) {
    return props.fields.slice(0, props.fieldsPerRow)
  }

  return props.fields
})

// 方法
const shouldShowLabel = (field: any) => {
  return field.showLabel !== false && props.showLabels !== false && field.label
}

const getFieldWidth = (field: any) => {
  if (field.width) {
    return field.width
  }

  // 根据字段类型自动设置宽度，增加响应式支持
  switch (field.type) {
    case 'daterange':
      return 'w-full sm:min-w-[280px] sm:max-w-[320px]'
    case 'select':
      return 'w-full sm:min-w-[160px] sm:max-w-[200px]'
    case 'date':
      return 'w-full sm:min-w-[140px] sm:max-w-[180px]'
    case 'number':
      return 'w-full sm:min-w-[120px] sm:max-w-[160px]'
    default:
      return 'w-full sm:min-w-[200px] sm:max-w-[240px]'
  }
}

const getIcon = (iconName: string) => {
  // 这里可以根据图标名称返回对应的图标组件
  // 简单实现，可以扩展支持更多图标库
  const iconMap: Record<string, any> = {
    search: Search,
    filter: Filter,
    calendar: Calendar,
    'calendar-days': CalendarDays,
    tag: Tag,
    settings: Settings,
    play: Play,
    square: Square,
    'alert-circle': AlertCircle,
    activity: Activity,
  }
  return iconMap[iconName] || Search
}

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleSearch = () => {
  emit('search', { ...formValues.value })
}

const handleReset = () => {
  initFormValues()
  emit('reset')
}

const handleTimeSortChange = (value: string) => {
  const order = value === 'none' ? null : (value as 'asc' | 'desc')
  formValues.value.timeSort = order
  emit('time-sort-change', order)
}

const handleStatusChange = (value: any) => {
  const status = value === 'all' ? null : value
  formValues.value.status = status
  emit('status-change', status)
}

const handleFilterChange = (value: any) => {
  const filter = value === 'all' ? null : value
  formValues.value.filter = filter
  emit('filter-change', filter)
}

const handleColumnToggle = (columnKey: string, checked: boolean) => {
  if (checked) {
    if (!visibleColumns.value.includes(columnKey)) {
      visibleColumns.value.push(columnKey)
    }
  } else {
    const index = visibleColumns.value.indexOf(columnKey)
    if (index > -1) {
      visibleColumns.value.splice(index, 1)
    }
  }

  formValues.value.visibleColumns = [...visibleColumns.value]
  emit('column-change', [...visibleColumns.value])
}

const resetColumnVisibility = () => {
  if (props.columnFilterOptions) {
    visibleColumns.value = props.columnFilterOptions
      .filter(option => option.defaultVisible !== false)
      .map(option => option.key)

    formValues.value.visibleColumns = [...visibleColumns.value]
    emit('column-change', [...visibleColumns.value])
  }
}

// 暴露方法给父组件
defineExpose({
  getFormValues: () => formValues.value,
  resetForm: handleReset,
  setFormValues: (values: Record<string, any>) => {
    Object.assign(formValues.value, values)
  },
  getVisibleColumns: () => visibleColumns.value,
  setVisibleColumns: (columns: string[]) => {
    visibleColumns.value = [...columns]
    formValues.value.visibleColumns = [...columns]
    emit('column-change', [...columns])
  },
})
</script>

<style scoped>
.matt-search-form {
  @apply w-full;
}

.matt-search-form--default {
  /* 默认样式 */
}

.matt-search-form--compact {
  @apply text-sm;
}

.matt-search-form--compact .h-9 {
  @apply h-8;
}

.matt-search-form--minimal {
  /* 最小化样式 */
}

.matt-search-form--minimal .bg-background\/50 {
  @apply bg-transparent border-none p-2;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .matt-search-form .flex-wrap {
    @apply flex-col items-stretch gap-3;
  }

  .matt-search-form .ml-auto {
    @apply ml-0 justify-center;
  }

  .matt-search-form .min-w-0 {
    @apply w-full;
  }

  /* 移动端按钮优化 */
  .matt-search-form .h-9 {
    @apply h-10;
  }

  /* 移动端字段标签优化 */
  .matt-search-form .whitespace-nowrap {
    @apply whitespace-normal text-xs;
  }

  /* 移动端选择器优化 */
  .matt-search-form .w-auto {
    @apply w-full;
  }
}

/* 平板适配 */
@media (min-width: 641px) and (max-width: 1024px) {
  .matt-search-form .flex-wrap {
    @apply gap-2;
  }

  /* 平板端字段宽度调整 */
  .matt-search-form [class*="min-w-"] {
    @apply flex-1 max-w-none;
  }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
  .matt-search-form .flex-wrap {
    @apply gap-4;
  }
}

/* 动画效果 */
.matt-search-form .transition-transform {
  transition: transform 0.2s ease-in-out;
}

/* 聚焦效果 */
.matt-search-form :deep(.focus\:ring-2) {
  @apply focus:ring-primary/20;
}

</style>
