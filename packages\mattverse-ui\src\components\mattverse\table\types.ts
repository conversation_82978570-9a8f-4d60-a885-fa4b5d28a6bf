// 简化类型定义，使用具体类型而不是复杂泛型
export type TableRecord = Record<string, any>

export interface MattTableColumn {
  /** 列标题 */
  title: string
  /** 数据字段名 */
  key: string
  /** 表头样式类 */
  headerClass?: string
  /** 单元格样式类 */
  cellClass?: string
  /** 单元格宽度 */
  width?: string | number
  /** 单元格最小宽度 */
  minWidth?: string | number
  /** 是否显示省略号 */
  ellipsis?: boolean
  /** 是否换行显示 */
  wrap?: boolean
  /** 是否显示提示 */
  tooltip?: boolean
  /** 是否使用插槽 */
  slot?: boolean
  /** 默认值 */
  defaultValue?: any
  /** 是否可复制 */
  copyable?: boolean
  /** 是否固定列 */
  fixed?: 'left' | 'right' | false
  /** 格式化函数 */
  formatter?: (value: any, record: TableRecord, index: number) => string | number
  /** 排序配置 */
  sortable?: boolean
  /** 筛选配置 */
  filterable?: boolean
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
}

export interface MattTableAction {
  /** 操作标题 */
  title: string
  /** 操作类型 */
  type?: 'primary' | 'secondary' | 'destructive' | 'ghost' | 'link'
  /** 操作图标 */
  icon?: string
  /** 点击事件 */
  onClick: (record: TableRecord, index: number) => void
  /** 是否显示 */
  visible?: (record: TableRecord, index: number) => boolean
  /** 是否禁用 */
  disabled?: (record: TableRecord, index: number) => boolean
  /** 确认提示 */
  confirm?: string
}

export interface MattTableProps<T = TableRecord> {
  /** 表格数据 */
  data: T[]
  /** 列配置 */
  columns: MattTableColumn[]
  /** 操作列配置 */
  actions?: MattTableAction[]
  /** 是否显示操作列 */
  showActions?: boolean
  /** 操作列标题 */
  actionsTitle?: string
  /** 操作列宽度 */
  actionsWidth?: string | number
  /** 是否显示边框 */
  bordered?: boolean
  /** 是否显示斑马纹 */
  striped?: boolean
  /** 是否悬停高亮 */
  hoverable?: boolean
  /** 表格大小 */
  size?: 'sm' | 'default' | 'lg'
  /** 加载状态 */
  loading?: boolean
  /** 空数据提示 */
  emptyText?: string
  /** 表格样式类 */
  class?: string
  /** 表格最大高度，超出时显示滚动条 */
  maxHeight?: string | number
  /** 表头背景色类名 */
  headerBgClass?: string
  /** 行选择配置 */
  rowSelection?: {
    type: 'checkbox' | 'radio'
    selectedRowKeys?: (string | number)[]
    onChange?: (selectedRowKeys: (string | number)[], selectedRows: T[]) => void
    getCheckboxProps?: (record: T) => { disabled?: boolean }
  }
  /** 行点击事件 */
  onRowClick?: (record: T, index: number) => void
  /** 行双击事件 */
  onRowDoubleClick?: (record: T, index: number) => void
  /** 排序变化事件 */
  onSortChange?: (key: string, order: 'asc' | 'desc' | null) => void
}

export interface MattTableEmits<T = TableRecord> {
  'row-click': [record: T, index: number]
  'row-double-click': [record: T, index: number]
  'selection-change': [selectedRowKeys: (string | number)[], selectedRows: T[]]
  'sort-change': [key: string, order: 'asc' | 'desc' | null]
}
